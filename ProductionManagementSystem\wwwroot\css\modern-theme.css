/* Импорт современных шрифтов */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap');

/* Основные переменные для темной темы */
:root {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  
  --bg-primary: #0f0f23;
  --bg-secondary: #1a1a2e;
  --bg-tertiary: #16213e;
  --bg-card: #1e1e3f;
  --bg-hover: #252550;
  
  --text-primary: #ffffff;
  --text-secondary: #b8b8d1;
  --text-muted: #8b8ba7;
  
  --border-color: #2d2d5a;
  --border-hover: #4a4a7a;
  
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.5);
  --shadow-glow: 0 0 20px rgba(102, 126, 234, 0.3);
  
  --border-radius: 12px;
  --border-radius-lg: 16px;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Базовые стили */
* {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

/* Layout с боковой панелью */
.app-container {
  display: flex;
  min-height: 100vh;
}

.sidebar {
  width: 280px;
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-color);
  position: fixed;
  height: 100vh;
  left: 0;
  top: 0;
  z-index: 1000;
  transition: var(--transition);
  overflow-y: auto;
}

.sidebar.collapsed {
  width: 80px;
}

.sidebar-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--primary-gradient);
  position: relative;
}

.sidebar-brand {
  color: white;
  text-decoration: none;
  font-weight: 700;
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.sidebar-brand:hover {
  color: white;
  text-decoration: none;
}

.sidebar-brand i {
  font-size: 1.5rem;
}

.sidebar-toggle {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
}

.sidebar-toggle:hover {
  background: rgba(255, 255, 255, 0.3);
}

.sidebar-nav {
  padding: 1rem 0;
}

.nav-section {
  margin-bottom: 2rem;
}

.nav-section-title {
  color: var(--text-muted);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  padding: 0 1.5rem;
  margin-bottom: 0.5rem;
}

.sidebar.collapsed .nav-section-title {
  display: none;
}

.nav-item {
  margin-bottom: 0.25rem;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1.5rem;
  color: var(--text-secondary);
  text-decoration: none;
  transition: var(--transition);
  border-radius: 0;
  position: relative;
}

.nav-link:hover {
  color: var(--text-primary);
  background: var(--bg-hover);
  text-decoration: none;
}

.nav-link.active {
  color: white;
  background: var(--primary-gradient);
  box-shadow: var(--shadow-glow);
}

.nav-link.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: white;
}

.nav-link i {
  font-size: 1.25rem;
  width: 20px;
  text-align: center;
}

.sidebar.collapsed .nav-link {
  padding: 0.75rem;
  justify-content: center;
}

.sidebar.collapsed .nav-link span {
  display: none;
}

/* Основной контент */
.main-content {
  flex: 1;
  margin-left: 280px;
  transition: var(--transition);
  background: var(--bg-primary);
}

.sidebar.collapsed + .main-content {
  margin-left: 80px;
}

.top-header {
  background: var(--bg-card);
  border-bottom: 1px solid var(--border-color);
  padding: 1rem 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: var(--shadow-sm);
}

.page-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-primary);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.content-area {
  padding: 2rem;
}

/* Современные карточки */
.card {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-md);
  transition: var(--transition);
  overflow: hidden;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--border-hover);
}

.card-header {
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-color);
  padding: 1.25rem 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.card-body {
  padding: 1.5rem;
  color: var(--text-secondary);
}

.card-footer {
  background: var(--bg-tertiary);
  border-top: 1px solid var(--border-color);
  padding: 1rem 1.5rem;
}

/* Статистические карточки */
.stats-card {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
  transition: var(--transition);
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
}

.stats-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.stats-card.success::before {
  background: var(--success-gradient);
}

.stats-card.warning::before {
  background: var(--warning-gradient);
}

.stats-card.danger::before {
  background: var(--danger-gradient);
}

.stats-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.stats-label {
  color: var(--text-muted);
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.stats-icon {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  font-size: 2rem;
  color: var(--text-muted);
  opacity: 0.3;
}

/* Современные кнопки */
.btn {
  border-radius: var(--border-radius);
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  border: none;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: var(--primary-gradient);
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  color: white;
}

.btn-secondary {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background: var(--bg-hover);
  border-color: var(--border-hover);
  color: var(--text-primary);
}

.btn-success {
  background: var(--success-gradient);
  color: white;
}

.btn-warning {
  background: var(--warning-gradient);
  color: white;
}

.btn-danger {
  background: var(--danger-gradient);
  color: white;
}

.btn-outline-primary {
  background: transparent;
  color: #667eea;
  border: 2px solid #667eea;
}

.btn-outline-primary:hover {
  background: var(--primary-gradient);
  color: white;
  border-color: transparent;
}

/* Современные таблицы */
.table {
  background: var(--bg-card);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  color: var(--text-secondary);
}

.table thead th {
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
  font-weight: 600;
  padding: 1rem;
  border-top: none;
}

.table tbody tr {
  border-bottom: 1px solid var(--border-color);
  transition: var(--transition);
}

.table tbody tr:hover {
  background: var(--bg-hover);
}

.table tbody td {
  padding: 1rem;
  border-top: none;
  vertical-align: middle;
}

.table tbody tr:last-child {
  border-bottom: none;
}

/* Формы */
.form-control {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--text-primary);
  padding: 0.75rem 1rem;
  transition: var(--transition);
}

.form-control:focus {
  background: var(--bg-card);
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  color: var(--text-primary);
}

.form-control::placeholder {
  color: var(--text-muted);
}

.form-label {
  color: var(--text-primary);
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.form-select {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--text-primary);
  padding: 0.75rem 1rem;
}

.form-select:focus {
  background: var(--bg-card);
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  color: var(--text-primary);
}

/* Алерты */
.alert {
  border-radius: var(--border-radius);
  border: none;
  padding: 1rem 1.5rem;
  margin-bottom: 1rem;
}

.alert-success {
  background: linear-gradient(135deg, rgba(79, 172, 254, 0.1) 0%, rgba(0, 242, 254, 0.1) 100%);
  color: #4facfe;
  border-left: 4px solid #4facfe;
}

.alert-warning {
  background: linear-gradient(135deg, rgba(67, 233, 123, 0.1) 0%, rgba(56, 249, 215, 0.1) 100%);
  color: #43e97b;
  border-left: 4px solid #43e97b;
}

.alert-danger {
  background: linear-gradient(135deg, rgba(250, 112, 154, 0.1) 0%, rgba(254, 225, 64, 0.1) 100%);
  color: #fa709a;
  border-left: 4px solid #fa709a;
}

.alert-info {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  color: #667eea;
  border-left: 4px solid #667eea;
}
