/* Импорт современных шрифтов */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap');

/* Основные переменные для темной темы */
:root {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);

  --bg-primary: #0f0f23;
  --bg-secondary: #1a1a2e;
  --bg-tertiary: #16213e;
  --bg-card: #1e1e3f;
  --bg-hover: #252550;

  --text-primary: #ffffff;
  --text-secondary: #b8b8d1;
  --text-muted: #8b8ba7;

  --border-color: #2d2d5a;
  --border-hover: #4a4a7a;

  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.5);
  --shadow-glow: 0 0 20px rgba(102, 126, 234, 0.3);

  --border-radius: 12px;
  --border-radius-lg: 16px;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Базовые стили */
* {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

/* Layout с боковой панелью */
.app-container {
  display: flex;
  min-height: 100vh;
}

.sidebar {
  width: 280px;
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-color);
  position: fixed;
  height: 100vh;
  left: 0;
  top: 0;
  z-index: 1000;
  transition: var(--transition);
  overflow-y: auto;
}

.sidebar.collapsed {
  width: 80px;
}

.sidebar-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--primary-gradient);
  position: relative;
}

.sidebar-brand {
  color: white;
  text-decoration: none;
  font-weight: 700;
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.sidebar-brand:hover {
  color: white;
  text-decoration: none;
}

.sidebar-brand i {
  font-size: 1.5rem;
}

.sidebar-toggle {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
}

.sidebar-toggle:hover {
  background: rgba(255, 255, 255, 0.3);
}

.sidebar-nav {
  padding: 1rem 0;
}

.nav-section {
  margin-bottom: 2rem;
}

.nav-section-title {
  color: var(--text-muted);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  padding: 0 1.5rem;
  margin-bottom: 0.5rem;
}

.sidebar.collapsed .nav-section-title {
  display: none;
}

.nav-item {
  margin-bottom: 0.25rem;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1.5rem;
  color: var(--text-secondary);
  text-decoration: none;
  transition: var(--transition);
  border-radius: 0;
  position: relative;
}

.nav-link:hover {
  color: var(--text-primary);
  background: var(--bg-hover);
  text-decoration: none;
}

.nav-link.active {
  color: white;
  background: var(--primary-gradient);
  box-shadow: var(--shadow-glow);
}

.nav-link.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: white;
}

.nav-link i {
  font-size: 1.25rem;
  width: 20px;
  text-align: center;
}

.sidebar.collapsed .nav-link {
  padding: 0.75rem;
  justify-content: center;
}

.sidebar.collapsed .nav-link span {
  display: none;
}

/* Основной контент */
.main-content {
  flex: 1;
  margin-left: 280px;
  transition: var(--transition);
  background: var(--bg-primary);
}

.sidebar.collapsed + .main-content {
  margin-left: 80px;
}

.top-header {
  background: var(--bg-card);
  border-bottom: 1px solid var(--border-color);
  padding: 1rem 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: var(--shadow-sm);
}

.page-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-primary);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.content-area {
  padding: 2rem;
}

/* Современные карточки */
.card {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-md);
  transition: var(--transition);
  overflow: hidden;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--border-hover);
}

.card-header {
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-color);
  padding: 1.25rem 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.card-body {
  padding: 1.5rem;
  color: var(--text-secondary);
}

.card-footer {
  background: var(--bg-tertiary);
  border-top: 1px solid var(--border-color);
  padding: 1rem 1.5rem;
}

/* Статистические карточки */
.stats-card {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
  transition: var(--transition);
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
}

.stats-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.stats-card.success::before {
  background: var(--success-gradient);
}

.stats-card.warning::before {
  background: var(--warning-gradient);
}

.stats-card.danger::before {
  background: var(--danger-gradient);
}

.stats-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.stats-label {
  color: var(--text-muted);
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.stats-icon {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  font-size: 2rem;
  color: var(--text-muted);
  opacity: 0.3;
}

/* Современные кнопки */
.btn {
  border-radius: var(--border-radius);
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  border: none;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: var(--primary-gradient);
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  color: white;
}

.btn-secondary {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background: var(--bg-hover);
  border-color: var(--border-hover);
  color: var(--text-primary);
}

.btn-success {
  background: var(--success-gradient);
  color: white;
}

.btn-warning {
  background: var(--warning-gradient);
  color: white;
}

.btn-danger {
  background: var(--danger-gradient);
  color: white;
}

.btn-outline-primary {
  background: transparent;
  color: #667eea;
  border: 2px solid #667eea;
}

.btn-outline-primary:hover {
  background: var(--primary-gradient);
  color: white;
  border-color: transparent;
}
/* Современные таблицы */
.table {
  background: var(--bg-card);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  color: var(--text-secondary);
}

.table thead th {
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
  font-weight: 600;
  padding: 1rem;
  border-top: none;
}

.table tbody tr {
  border-bottom: 1px solid var(--border-color);
  transition: var(--transition);
}

.table tbody tr:hover {
  background: var(--bg-hover);
}

.table tbody td {
  padding: 1rem;
  border-top: none;
  vertical-align: middle;
}

.table tbody tr:last-child {
  border-bottom: none;
}

/* Формы */
.form-control {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--text-primary);
  padding: 0.75rem 1rem;
  transition: var(--transition);
}

.form-control:focus {
  background: var(--bg-card);
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  color: var(--text-primary);
}

.form-control::placeholder {
  color: var(--text-muted);
}

.form-label {
  color: var(--text-primary);
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.form-select {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--text-primary);
  padding: 0.75rem 1rem;
}

.form-select:focus {
  background: var(--bg-card);
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  color: var(--text-primary);
}

/* Алерты */
.alert {
  border-radius: var(--border-radius);
  border: none;
  padding: 1rem 1.5rem;
  margin-bottom: 1rem;
}

.alert-success {
  background: linear-gradient(135deg, rgba(79, 172, 254, 0.1) 0%, rgba(0, 242, 254, 0.1) 100%);
  color: #4facfe;
  border-left: 4px solid #4facfe;
}

.alert-warning {
  background: linear-gradient(135deg, rgba(67, 233, 123, 0.1) 0%, rgba(56, 249, 215, 0.1) 100%);
  color: #43e97b;
  border-left: 4px solid #43e97b;
}

.alert-danger {
  background: linear-gradient(135deg, rgba(250, 112, 154, 0.1) 0%, rgba(254, 225, 64, 0.1) 100%);
  color: #fa709a;
  border-left: 4px solid #fa709a;
}

.alert-info {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  color: #667eea;
  border-left: 4px solid #667eea;
}

/* Бейджи */
.badge {
  border-radius: 6px;
  font-weight: 500;
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
}

.badge.bg-success {
  background: var(--success-gradient) !important;
}

.badge.bg-warning {
  background: var(--warning-gradient) !important;
}

.badge.bg-danger {
  background: var(--danger-gradient) !important;
}

.badge.bg-primary {
  background: var(--primary-gradient) !important;
}

/* Прогресс-бары */
.progress {
  background: var(--bg-tertiary);
  border-radius: 10px;
  height: 8px;
  overflow: hidden;
}

.progress-bar {
  background: var(--primary-gradient);
  transition: width 0.6s ease;
}

/* Модальные окна */
.modal-content {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
}

.modal-header {
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.modal-title {
  color: var(--text-primary);
}

.modal-body {
  color: var(--text-secondary);
}

.modal-footer {
  background: var(--bg-tertiary);
  border-top: 1px solid var(--border-color);
  border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
}

/* Адаптивные стили */
@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .sidebar.show {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0;
  }

  .content-area {
    padding: 1rem;
  }

  .stats-card {
    margin-bottom: 1rem;
  }

  .page-title {
    font-size: 1.25rem;
  }
}

@media (max-width: 992px) {
  .sidebar {
    width: 260px;
  }

  .main-content {
    margin-left: 260px;
  }

  .sidebar.collapsed + .main-content {
    margin-left: 80px;
  }
}

/* Анимации */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.fade-in {
  animation: fadeIn 0.6s ease-out;
}

.slide-in {
  animation: slideIn 0.4s ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

/* Стили для статусов */
.status-pending {
  color: #f093fb;
}

.status-in-progress {
  color: #4facfe;
}

.status-completed {
  color: #43e97b;
}

.status-cancelled {
  color: #fa709a;
}

/* Утилиты */
.text-gradient {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.bg-gradient-primary {
  background: var(--primary-gradient);
}

.bg-gradient-secondary {
  background: var(--secondary-gradient);
}

.bg-gradient-success {
  background: var(--success-gradient);
}

.bg-gradient-warning {
  background: var(--warning-gradient);
}

.bg-gradient-danger {
  background: var(--danger-gradient);
}

/* Скроллбар */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--border-hover);
}

/* Дополнительные стили для производственных линий */
.production-line-card {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  transition: var(--transition);
  overflow: hidden;
}

.production-line-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.production-line-active {
  border-color: #43e97b;
  box-shadow: 0 0 20px rgba(67, 233, 123, 0.3);
}

.production-line-stopped {
  border-color: var(--text-muted);
  opacity: 0.7;
}

/* Конец стилей */





/* Стили для Dashboard */
.dashboard-card {
  border-radius: 1rem;
  overflow: hidden;
}

.dashboard-stat {
  font-size: 2rem;
  font-weight: bold;
}

/* Стили для статистических карточек */
.border-left-primary,
.border-left-success,
.border-left-info,
.border-left-warning,
.border-left-danger,
.border-left-secondary {
  border-radius: 0.5rem;
  transition: transform 0.2s ease-in-out;
}

.border-left-primary:hover,
.border-left-success:hover,
.border-left-info:hover,
.border-left-warning:hover,
.border-left-danger:hover,
.border-left-secondary:hover {
  transform: translateY(-2px);
}

/* Обеспечиваем равную высоту статистических карточек */
.row.mb-4 > [class*="col-"] .card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.row.mb-4 > [class*="col-"] .card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* Стили для форм */
.form-control:focus {
  border-color: #4e73df;
  box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* Стили для навигации */
.navbar-brand {
  font-size: 1.25rem;
}

.dropdown-menu {
  border-radius: 0.5rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Улучшенные стили для навигации */
.navbar-nav .nav-link {
  font-weight: 600 !important;
  padding: 0.75rem 1rem;
  border-radius: 0.375rem;
  margin: 0 0.25rem;
  transition: all 0.3s ease;
  color: #ffffff !important;
  background-color: rgba(255, 255, 255, 0.15) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

.navbar-nav .nav-link:hover {
  background-color: rgba(255, 255, 255, 0.25) !important;
  transform: translateY(-1px);
  color: #ffffff !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
}

.navbar-nav .nav-link.active {
  background-color: rgba(255, 255, 255, 0.3) !important;
  font-weight: 700 !important;
  color: #ffffff !important;
}

/* Стили для dropdown в навигации */
.navbar-nav .dropdown-toggle::after {
  display: none; /* Скрываем стандартную стрелку Bootstrap */
}

.navbar-nav .dropdown:hover .dropdown-menu {
  display: block;
  margin-top: 0;
}

.navbar-nav .dropdown-menu {
  border: none;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  border-radius: 0.5rem;
  padding: 0.5rem 0;
  min-width: 220px;
}

.navbar-nav .dropdown-item {
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.navbar-nav .dropdown-item:hover {
  background-color: #f8f9fa;
  transform: translateX(5px);
}

.navbar-nav .dropdown-header {
  font-weight: 600;
  color: #6c757d;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Индикаторы активного состояния */
.navbar-nav .nav-item.active .nav-link {
  background-color: rgba(255, 255, 255, 0.3) !important;
  font-weight: 700 !important;
  color: #ffffff !important;
  border: 1px solid rgba(255, 255, 255, 0.5) !important;
}

/* Адаптивные стили для мобильных устройств */
@media (max-width: 991px) {
  .navbar-nav .nav-link {
    margin: 0.25rem 0;
    padding: 0.75rem 1rem;
  }

  .navbar-nav .dropdown:hover .dropdown-menu {
    display: none; /* Отключаем hover на мобильных */
  }

  .navbar-nav .dropdown-menu {
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 0, 0, 0.1);
  }

  /* Улучшенная адаптивность для кнопок и фильтров */
  .page-header {
    flex-direction: column;
    align-items: stretch !important;
    gap: 1rem;
  }

  .page-header .d-flex {
    justify-content: center;
  }

  .page-header .btn {
    min-width: auto;
    flex: 1;
  }

  /* Адаптивные фильтры */
  .filter-card .row.g-3 > [class*="col-"] {
    margin-bottom: 1rem;
  }

  .filter-card .d-flex.gap-2 {
    flex-direction: column;
    gap: 0.5rem !important;
  }

  .filter-card .btn {
    width: 100%;
  }
}

/* Стили для footer */
.footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}

/* Дополнительные стили для улучшения отображения */
@media (min-width: 1200px) {
  .production-line-card {
    min-height: 450px; /* Немного меньше на больших экранах */
  }
}

@media (max-width: 991px) {
  .production-line-card {
    min-height: 400px; /* Еще меньше на планшетах */
  }
}

/* Стили для предотвращения переполнения текста */
.production-line-card .card-header h6 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.production-line-card .alert-heading {
  font-size: 0.9rem;
}

/* Стили для кнопок в карточках */
.production-line-card .btn-sm {
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
}

/* Улучшенные стили для статистики в карточках */
.production-line-card .border-end:last-child {
  border-right: none !important;
}

/* Стили для обеспечения корректного отображения dropdown */
.production-line-card .dropdown {
  position: relative;
}

.production-line-card .dropdown-menu {
  position: absolute;
  top: 100%;
  left: auto;
  right: 0;
  z-index: 1050;
  min-width: 160px;
}

/* Улучшенные стили для кнопок создания и фильтров */
.page-header {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e3e6f0;
}

.page-header .btn {
  min-width: 150px;
  font-weight: 500;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  transition: all 0.3s ease;
}

.page-header .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
}

/* Стили для карточек фильтров */
.filter-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  border-radius: 0.75rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.filter-card .card-body {
  padding: 1.5rem;
}

.filter-card .form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
}

.filter-card .btn {
  min-width: 100px;
  font-weight: 500;
}

/* Обеспечиваем видимость элементов на всех экранах */
.btn, .form-control, .form-select {
  position: relative;
  z-index: 1;
}

/* Стили для улучшения контрастности */
.btn-primary {
  background-color: #007bff !important;
  border: 2px solid #007bff !important;
  color: white !important;
  font-weight: 600 !important;
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3) !important;
}

.btn-primary:hover {
  background-color: #0056b3 !important;
  border-color: #0056b3 !important;
  color: white !important;
  box-shadow: 0 6px 12px rgba(0, 86, 179, 0.4) !important;
}

.btn-outline-primary {
  border: 2px solid #007bff !important;
  color: #007bff !important;
  font-weight: 600 !important;
  background-color: white !important;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2) !important;
}

.btn-outline-primary:hover {
  background-color: #007bff !important;
  border-color: #007bff !important;
  color: white !important;
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3) !important;
}

/* Дополнительные стили для обеспечения видимости */
.btn {
  display: inline-flex !important;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  text-decoration: none !important;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
  transition: all 0.15s ease-in-out;
}

.btn:focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* Стили для обеспечения видимости на светлом фоне */
.card {
  background-color: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.125);
}

.form-control, .form-select {
  background-color: #ffffff;
  border: 1px solid #d1d3e2;
  border-radius: 0.35rem;
}

.form-control:focus, .form-select:focus {
  background-color: #ffffff;
  border-color: #4e73df;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* Улучшенные стили для dropdown */
.dropdown-menu {
  background-color: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 0.5rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.175);
  z-index: 1000;
}

.dropdown-item {
  color: #3a3b45;
  text-decoration: none;
  background-color: transparent;
  border: 0;
  display: block;
  width: 100%;
  padding: 0.5rem 1rem;
  clear: both;
  font-weight: 400;
  text-align: inherit;
  white-space: nowrap;
  border-radius: 0;
}

.dropdown-item:hover, .dropdown-item:focus {
  color: #16181b;
  background-color: #f8f9fa;
}

/* Дополнительные стили для очень маленьких экранов */
@media (max-width: 576px) {
  .page-header h1 {
    font-size: 1.5rem;
  }

  .page-header .btn {
    font-size: 0.8rem;
    padding: 0.5rem 1rem;
  }

  .filter-card .card-body {
    padding: 1rem;
  }

  .filter-card .form-label {
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
  }

  .filter-card .btn {
    font-size: 0.8rem;
    padding: 0.5rem;
  }

  /* Стек кнопок на очень маленьких экранах */
  .d-flex.gap-2 {
    flex-direction: column !important;
    gap: 0.5rem !important;
  }

  .btn-group {
    flex-direction: column;
    width: 100%;
  }

  .btn-group .btn {
    border-radius: 0.375rem !important;
    margin-bottom: 0.25rem;
  }
}

/* Стили для обеспечения видимости на всех устройствах */
@media (min-width: 576px) and (max-width: 767px) {
  .page-header .d-flex {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .filter-card .row.g-3 > [class*="col-"] {
    margin-bottom: 0.75rem;
  }
}

/* Улучшенные стили для планшетов */
@media (min-width: 768px) and (max-width: 991px) {
  .page-header .btn {
    min-width: 120px;
  }

  .filter-card .btn {
    min-width: 80px;
  }
}

/* Принудительная видимость для всех кнопок */
.btn, .btn-primary, .btn-outline-primary, .btn-outline-secondary, .btn-outline-info {
  visibility: visible !important;
  opacity: 1 !important;
  display: inline-flex !important;
  position: relative !important;
  z-index: 100 !important;
  min-height: 38px !important;
  padding: 8px 16px !important;
  font-size: 14px !important;
  font-weight: 600 !important;
}

/* Обеспечиваем контрастность текста */
.btn-primary {
  color: #ffffff !important;
  background-color: #007bff !important;
  border-color: #007bff !important;
}

.btn-outline-primary {
  color: #007bff !important;
  background-color: #ffffff !important;
  border-color: #007bff !important;
}

.btn-outline-secondary {
  color: #6c757d !important;
  background-color: #ffffff !important;
  border: 2px solid #6c757d !important;
  font-weight: 600 !important;
}

.btn-outline-info {
  color: #17a2b8 !important;
  background-color: #ffffff !important;
  border: 2px solid #17a2b8 !important;
  font-weight: 600 !important;
}

.btn-success {
  background-color: #28a745 !important;
  border: 2px solid #28a745 !important;
  color: white !important;
  font-weight: 600 !important;
  box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3) !important;
}

.btn-success:hover {
  background-color: #218838 !important;
  border-color: #218838 !important;
  color: white !important;
  box-shadow: 0 6px 12px rgba(33, 136, 56, 0.4) !important;
}