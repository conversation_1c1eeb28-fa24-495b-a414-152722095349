@model IEnumerable<ProductionManagementSystem.Models.ProductionLine>
@{
    ViewData["Title"] = "Производственные линии";
}

<div class="d-flex justify-content-between align-items-center mb-4 page-header">
    <h1 class="h2 mb-0">
        <i class="bi bi-diagram-3 me-2 text-primary"></i>Производственные линии
    </h1>
    <div class="d-flex gap-2">
        <a asp-action="Schedule" class="btn btn-outline-info shadow-sm">
            <i class="bi bi-calendar3 me-2"></i>Расписание
        </a>
        <a asp-action="Create" class="btn btn-primary shadow-sm">
            <i class="bi bi-plus-circle me-2"></i>Добавить линию
        </a>
    </div>
</div>

<!-- Статистика -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-left-success">
            <div class="card-body">
                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Активные</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">
                    @Model.Count(pl => pl.Status == ProductionManagementSystem.Models.ProductionLineStatus.Active)
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-secondary">
            <div class="card-body">
                <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">Остановленные</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">
                    @Model.Count(pl => pl.Status == ProductionManagementSystem.Models.ProductionLineStatus.Stopped)
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-info">
            <div class="card-body">
                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Доступные</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">
                    @Model.Count(pl => pl.IsAvailable)
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-warning">
            <div class="card-body">
                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Занятые</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">
                    @Model.Count(pl => pl.CurrentWorkOrderId != null)
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Карточки производственных линий -->
<div class="row production-lines-row">
    @foreach (var line in Model)
    {
        <div class="col-lg-6 col-xl-4">
            <div class="card production-line-card h-100 @(line.Status == ProductionManagementSystem.Models.ProductionLineStatus.Active ? "border-success" : "border-secondary")"
                 data-production-line-id="@line.Id">
                <div class="card-header @(line.Status == ProductionManagementSystem.Models.ProductionLineStatus.Active ? "bg-success" : "bg-secondary") text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0 fw-bold">@line.Name</h6>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-light dropdown-toggle" type="button"
                                    data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-gear"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li>
                                    <a class="dropdown-item" asp-action="Details" asp-route-id="@line.Id">
                                        <i class="bi bi-eye me-2"></i>Подробности
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" asp-action="Edit" asp-route-id="@line.Id">
                                        <i class="bi bi-pencil me-2"></i>Редактировать
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <form asp-action="ToggleStatus" asp-route-id="@line.Id" method="post" class="d-inline">
                                        <button type="submit" class="dropdown-item">
                                            <i class="bi @(line.Status == ProductionManagementSystem.Models.ProductionLineStatus.Active ? "bi-pause-circle" : "bi-play-circle") me-2"></i>
                                            @(line.Status == ProductionManagementSystem.Models.ProductionLineStatus.Active ? "Остановить" : "Запустить")
                                        </button>
                                    </form>
                                </li>
                                @if (line.CurrentWorkOrder != null && line.CurrentWorkOrder.Status == ProductionManagementSystem.Models.WorkOrderStatus.Pending)
                                {
                                    <li>
                                        <form asp-action="ReleaseOrder" asp-route-id="@line.Id" method="post" class="d-inline">
                                            <button type="submit" class="dropdown-item text-warning">
                                                <i class="bi bi-x-circle me-2"></i>Снять заказ
                                            </button>
                                        </form>
                                    </li>
                                }
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Статус и эффективность -->
                    <div class="row mb-3">
                        <div class="col-6">
                            <small class="text-muted">Статус:</small><br>
                            <span class="badge status-badge @(line.Status == ProductionManagementSystem.Models.ProductionLineStatus.Active ? "bg-success" : "bg-secondary")">
                                @(line.Status == ProductionManagementSystem.Models.ProductionLineStatus.Active ? "Активна" : "Остановлена")
                            </span>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">Эффективность:</small><br>
                            <span class="fw-bold">@line.EfficiencyFactor.ToString("F1")</span>
                        </div>
                    </div>

                    <!-- Текущий заказ -->
                    @if (line.CurrentWorkOrder != null)
                    {
                        <div class="alert alert-info mb-3">
                            <h6 class="alert-heading mb-2">
                                <i class="bi bi-clipboard-check me-1"></i>Текущий заказ
                            </h6>
                            <div class="row">
                                <div class="col-12">
                                    <strong>Заказ #@line.CurrentWorkOrder.Id</strong><br>
                                    <small>@line.CurrentWorkOrder.Product.Name</small><br>
                                    <small class="text-muted">Количество: @line.CurrentWorkOrder.Quantity</small>
                                </div>
                            </div>
                            @if (line.CurrentWorkOrder.Status == ProductionManagementSystem.Models.WorkOrderStatus.InProgress)
                            {
                                <div class="progress mt-2" style="height: 8px;">
                                    <div class="progress-bar bg-info" role="progressbar"
                                         style="width: @line.CurrentWorkOrder.Progress%"
                                         aria-valuenow="@line.CurrentWorkOrder.Progress"
                                         aria-valuemin="0"
                                         aria-valuemax="100">
                                    </div>
                                </div>
                                <small class="text-muted">Прогресс: @line.CurrentWorkOrder.Progress%</small>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-light mb-3">
                            <div class="text-center">
                                <i class="bi bi-inbox display-6 text-muted"></i>
                                <p class="text-muted mb-0 mt-2">Нет активного заказа</p>
                                @if (line.Status == ProductionManagementSystem.Models.ProductionLineStatus.Active)
                                {
                                    <button type="button" class="btn btn-sm btn-outline-primary mt-2"
                                            onclick="showAssignOrderModal(@line.Id, '@line.Name')">
                                        <i class="bi bi-plus-circle me-1"></i>Назначить заказ
                                    </button>
                                }
                            </div>
                        </div>
                    }

                    <!-- Статистика линии -->
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="border-end">
                                <div class="h6 text-primary">
                                    @(line.WorkOrders?.Count(wo => wo.Status == ProductionManagementSystem.Models.WorkOrderStatus.Completed) ?? 0)
                                </div>
                                <small class="text-muted">Завершено</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="border-end">
                                <div class="h6 text-warning">
                                    @(line.WorkOrders?.Count(wo => wo.Status == ProductionManagementSystem.Models.WorkOrderStatus.Pending) ?? 0)
                                </div>
                                <small class="text-muted">В очереди</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="h6 text-info">
                                @(line.WorkOrders?.Count(wo => wo.Status == ProductionManagementSystem.Models.WorkOrderStatus.InProgress) ?? 0)
                            </div>
                            <small class="text-muted">В работе</small>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            @if (line.IsAvailable)
                            {
                                <i class="bi bi-check-circle text-success me-1"></i><span>Доступна</span>
                            }
                            else
                            {
                                <i class="bi bi-x-circle text-danger me-1"></i><span>Занята</span>
                            }
                        </small>
                        <a asp-action="Details" asp-route-id="@line.Id" class="btn btn-sm btn-outline-primary">
                            Подробнее <i class="bi bi-arrow-right"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@if (!Model.Any())
{
    <div class="text-center py-5">
        <i class="bi bi-diagram-3 display-1 text-muted"></i>
        <h4 class="text-muted mt-3">Нет производственных линий</h4>
        <p class="text-muted">
            Начните с <a asp-action="Create" class="text-decoration-none">создания первой производственной линии</a>
        </p>
    </div>
}

<!-- Модальное окно для назначения заказа -->
<div class="modal fade" id="assignOrderModal" tabindex="-1" aria-labelledby="assignOrderModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="assignOrderModalLabel">Назначить заказ на линию</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="assignOrderContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Загрузка...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отмена</button>
                <button type="button" class="btn btn-primary" id="assignOrderBtn" onclick="assignOrder()" disabled>
                    Назначить заказ
                </button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        let selectedLineId = null;
        let selectedOrderId = null;

        // Показать модальное окно назначения заказа
        async function showAssignOrderModal(lineId, lineName) {
            selectedLineId = lineId;
            document.getElementById('assignOrderModalLabel').textContent = `Назначить заказ на линию "${lineName}"`;

            const modal = new bootstrap.Modal(document.getElementById('assignOrderModal'));
            modal.show();

            // Загружаем доступные заказы
            try {
                const response = await fetch('/ProductionLines/GetAvailableOrders');
                if (response.ok) {
                    const orders = await response.json();
                    displayAvailableOrders(orders);
                } else {
                    document.getElementById('assignOrderContent').innerHTML =
                        '<div class="alert alert-danger">Ошибка при загрузке заказов</div>';
                }
            } catch (error) {
                console.error('Ошибка:', error);
                document.getElementById('assignOrderContent').innerHTML =
                    '<div class="alert alert-danger">Ошибка при загрузке заказов</div>';
            }
        }

        // Отображение доступных заказов
        function displayAvailableOrders(orders) {
            const content = document.getElementById('assignOrderContent');

            if (orders.length === 0) {
                content.innerHTML = `
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        Нет доступных заказов для назначения
                    </div>
                `;
                return;
            }

            let html = `
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="50"></th>
                                <th>№</th>
                                <th>Продукт</th>
                                <th>Количество</th>
                                <th>Дата начала</th>
                                <th>Плановое завершение</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            orders.forEach(order => {
                html += `
                    <tr class="order-row" data-order-id="${order.id}" onclick="selectOrder(${order.id})">
                        <td>
                            <input type="radio" name="selectedOrder" value="${order.id}" class="form-check-input">
                        </td>
                        <td>#${order.id}</td>
                        <td>${order.productName}</td>
                        <td>${order.quantity}</td>
                        <td>${order.startDate}</td>
                        <td>${order.estimatedEndDate}</td>
                    </tr>
                `;
            });

            html += '</tbody></table></div>';
            content.innerHTML = html;
        }

        // Выбор заказа
        function selectOrder(orderId) {
            selectedOrderId = orderId;

            // Снимаем выделение со всех строк
            document.querySelectorAll('.order-row').forEach(row => {
                row.classList.remove('table-primary');
            });

            // Выделяем выбранную строку
            const selectedRow = document.querySelector(`[data-order-id="${orderId}"]`);
            selectedRow.classList.add('table-primary');

            // Отмечаем радиокнопку
            const radio = selectedRow.querySelector('input[type="radio"]');
            radio.checked = true;

            // Включаем кнопку назначения
            document.getElementById('assignOrderBtn').disabled = false;
        }

        // Назначить заказ
        async function assignOrder() {
            if (!selectedLineId || !selectedOrderId) {
                alert('Выберите заказ для назначения');
                return;
            }

            try {
                const response = await fetch('/ProductionLines/AssignOrder', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `lineId=${selectedLineId}&orderId=${selectedOrderId}&__RequestVerificationToken=${getAntiForgeryToken()}`
                });

                if (response.ok) {
                    bootstrap.Modal.getInstance(document.getElementById('assignOrderModal')).hide();
                    location.reload();
                } else {
                    alert('Ошибка при назначении заказа');
                }
            } catch (error) {
                console.error('Ошибка:', error);
                alert('Ошибка при назначении заказа');
            }
        }

        // Получение CSRF токена
        function getAntiForgeryToken() {
            const token = document.querySelector('input[name="__RequestVerificationToken"]');
            return token ? token.value : '';
        }

        // Автоматическое обновление статусов каждые 30 секунд
        setInterval(() => {
            location.reload();
        }, 30000);
    </script>
}
