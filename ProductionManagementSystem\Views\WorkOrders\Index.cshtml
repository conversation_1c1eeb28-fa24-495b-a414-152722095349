@model IEnumerable<ProductionManagementSystem.Models.WorkOrder>
@{
    ViewData["Title"] = "Производственные заказы";
}

<div class="d-flex justify-content-between align-items-center mb-4 page-header">
    <h1 class="h2 mb-0">
        <i class="bi bi-clipboard-check me-2 text-primary"></i>Производственные заказы
    </h1>
    <div class="d-flex gap-2">
        <a asp-action="Create" class="btn btn-primary shadow-sm">
            <i class="bi bi-plus-circle me-2"></i>Создать заказ
        </a>
    </div>
</div>

<!-- Фильтры -->
<div class="card mb-4 filter-card">
    <div class="card-header bg-transparent border-0 pb-0">
        <h6 class="mb-0 text-primary">
            <i class="bi bi-funnel me-2"></i>Фильтры заказов
        </h6>
    </div>
    <div class="card-body pt-3">
        <form method="get" class="row g-3">
            <div class="col-lg-3 col-md-6">
                <label for="status" class="form-label">
                    <i class="bi bi-flag me-1"></i>Статус заказа
                </label>
                <select class="form-select shadow-sm" id="status" name="status">
                    <option value="">Все статусы</option>
                    <option value="Pending" selected="@(ViewBag.Status?.ToString() == "Pending")">Ожидание</option>
                    <option value="InProgress" selected="@(ViewBag.Status?.ToString() == "InProgress")">В работе</option>
                    <option value="Completed" selected="@(ViewBag.Status?.ToString() == "Completed")">Завершен</option>
                    <option value="Cancelled" selected="@(ViewBag.Status?.ToString() == "Cancelled")">Отменен</option>
                </select>
            </div>
            <div class="col-lg-3 col-md-6">
                <label for="date" class="form-label">
                    <i class="bi bi-calendar me-1"></i>Дата начала
                </label>
                <input type="date" class="form-control shadow-sm" id="date" name="date"
                       value="@(ViewBag.Date?.ToString("yyyy-MM-dd"))" />
            </div>
            <div class="col-lg-3 col-md-6">
                <label class="form-label d-block">
                    <i class="bi bi-exclamation-triangle me-1"></i>Дополнительно
                </label>
                <div class="form-check mt-2">
                    <input class="form-check-input" type="checkbox" id="overdue" name="overdue"
                           value="true" @(ViewBag.Overdue == true ? "checked" : "") />
                    <label class="form-check-label" for="overdue">
                        Только просроченные заказы
                    </label>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 d-flex align-items-end gap-2">
                <button type="submit" class="btn btn-outline-primary shadow-sm flex-fill">
                    <i class="bi bi-search me-1"></i>Применить
                </button>
                <a href="@Url.Action("Index")" class="btn btn-outline-secondary shadow-sm flex-fill">
                    <i class="bi bi-arrow-clockwise me-1"></i>Сброс
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Статистика -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-left-warning">
            <div class="card-body">
                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Ожидающие</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">
                    @Model.Count(wo => wo.Status == ProductionManagementSystem.Models.WorkOrderStatus.Pending)
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-info">
            <div class="card-body">
                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">В работе</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">
                    @Model.Count(wo => wo.Status == ProductionManagementSystem.Models.WorkOrderStatus.InProgress)
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-success">
            <div class="card-body">
                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Завершенные</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">
                    @Model.Count(wo => wo.Status == ProductionManagementSystem.Models.WorkOrderStatus.Completed)
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-danger">
            <div class="card-body">
                <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Просроченные</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">
                    @Model.Count(wo => wo.IsOverdue)
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Таблица заказов -->
<div class="card shadow">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">
            Список заказов (@Model.Count())
        </h6>
    </div>
    <div class="card-body">
        @if (Model.Any())
        {
            <div class="table-responsive">
                <table class="table table-hover" id="ordersTable">
                    <thead class="table-light">
                        <tr>
                            <th>№</th>
                            <th>Продукт</th>
                            <th>Количество</th>
                            <th>Статус</th>
                            <th>Прогресс</th>
                            <th>Дата начала</th>
                            <th>Плановое завершение</th>
                            <th>Производственная линия</th>
                            <th>Действия</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var order in Model)
                        {
                            <tr class="@(order.IsOverdue ? "table-danger" : "")" data-order-id="@order.Id">
                                <td>
                                    <a href="@Url.Action("Details", new { id = order.Id })" class="text-decoration-none fw-bold">
                                        #@order.Id
                                    </a>
                                </td>
                                <td>
                                    <div class="fw-bold">@order.Product.Name</div>
                                    <small class="text-muted">@order.Product.Category</small>
                                </td>
                                <td>@order.Quantity</td>
                                <td>
                                    @switch (order.Status)
                                    {
                                        case ProductionManagementSystem.Models.WorkOrderStatus.Pending:
                                            <span class="badge bg-warning text-dark">
                                                <i class="bi bi-clock me-1"></i>Ожидание
                                            </span>
                                            break;
                                        case ProductionManagementSystem.Models.WorkOrderStatus.InProgress:
                                            <span class="badge bg-info">
                                                <i class="bi bi-play-circle me-1"></i>В работе
                                            </span>
                                            break;
                                        case ProductionManagementSystem.Models.WorkOrderStatus.Completed:
                                            <span class="badge bg-success">
                                                <i class="bi bi-check-circle me-1"></i>Завершен
                                            </span>
                                            break;
                                        case ProductionManagementSystem.Models.WorkOrderStatus.Cancelled:
                                            <span class="badge bg-danger">
                                                <i class="bi bi-x-circle me-1"></i>Отменен
                                            </span>
                                            break;
                                    }
                                    @if (order.IsOverdue)
                                    {
                                        <br><small class="text-danger"><i class="bi bi-exclamation-triangle"></i> Просрочен</small>
                                    }
                                </td>
                                <td>
                                    @if (order.Status == ProductionManagementSystem.Models.WorkOrderStatus.InProgress)
                                    {
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar @(order.Progress < 50 ? "bg-warning" : order.Progress < 100 ? "bg-info" : "bg-success")"
                                                 role="progressbar"
                                                 style="width: @order.Progress%"
                                                 aria-valuenow="@order.Progress"
                                                 aria-valuemin="0"
                                                 aria-valuemax="100">
                                                @order.Progress%
                                            </div>
                                        </div>
                                    }
                                    else
                                    {
                                        <span class="text-muted">@order.Progress%</span>
                                    }
                                </td>
                                <td>@order.StartDate.ToString("dd.MM.yyyy HH:mm")</td>
                                <td>
                                    @order.EstimatedEndDate.ToString("dd.MM.yyyy HH:mm")
                                    @if (order.IsOverdue)
                                    {
                                        <br><small class="text-danger">Просрочен на @((DateTime.Now - order.EstimatedEndDate).Days) дн.</small>
                                    }
                                </td>
                                <td>
                                    @if (order.ProductionLine != null)
                                    {
                                        <span class="badge bg-secondary">@order.ProductionLine.Name</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted">Не назначена</span>
                                    }
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a asp-action="Details" asp-route-id="@order.Id"
                                           class="btn btn-sm btn-outline-info" title="Подробности">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        @if (order.Status == ProductionManagementSystem.Models.WorkOrderStatus.InProgress)
                                        {
                                            <button type="button" class="btn btn-sm btn-outline-primary"
                                                    title="Обновить прогресс"
                                                    onclick="showProgressModal(@order.Id, @order.Progress)">
                                                <i class="bi bi-arrow-up-circle"></i>
                                            </button>
                                        }
                                        @if (order.Status == ProductionManagementSystem.Models.WorkOrderStatus.Pending ||
                                             order.Status == ProductionManagementSystem.Models.WorkOrderStatus.InProgress)
                                        {
                                            <button type="button" class="btn btn-sm btn-outline-danger"
                                                    title="Отменить заказ"
                                                    onclick="cancelOrder(@order.Id)">
                                                <i class="bi bi-x-circle"></i>
                                            </button>
                                        }
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="text-center py-5">
                <i class="bi bi-clipboard-x display-1 text-muted"></i>
                <h4 class="text-muted mt-3">Заказы не найдены</h4>
                <p class="text-muted">
                    @if (ViewBag.Status != null || ViewBag.Date != null || ViewBag.Overdue == true)
                    {
                        <span>Попробуйте изменить параметры фильтра или </span>
                        <a href="@Url.Action("Index")" class="text-decoration-none">сбросить фильтры</a>
                    }
                    else
                    {
                        <span>Начните с </span>
                        <a asp-action="Create" class="text-decoration-none">создания первого заказа</a>
                    }
                </p>
            </div>
        }
    </div>
</div>

<!-- Модальное окно для обновления прогресса -->
<div class="modal fade" id="progressModal" tabindex="-1" aria-labelledby="progressModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="progressModalLabel">Обновить прогресс заказа</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="progressForm">
                    <input type="hidden" id="progressOrderId" />
                    <div class="mb-3">
                        <label for="progressValue" class="form-label">Прогресс выполнения (%)</label>
                        <input type="range" class="form-range" id="progressValue" min="0" max="100" step="5" />
                        <div class="d-flex justify-content-between">
                            <span>0%</span>
                            <span id="progressDisplay">50%</span>
                            <span>100%</span>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отмена</button>
                <button type="button" class="btn btn-primary" onclick="updateProgress()">Обновить</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Инициализация DataTable
        $(document).ready(function() {
            if ($('#ordersTable tbody tr').length > 0) {
                $('#ordersTable').DataTable({
                    "language": {
                        "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Russian.json"
                    },
                    "pageLength": 25,
                    "order": [[0, "desc"]],
                    "columnDefs": [
                        { "orderable": false, "targets": [4, 8] }
                    ]
                });
            }
        });

        // Показать модальное окно прогресса
        function showProgressModal(orderId, currentProgress) {
            document.getElementById('progressOrderId').value = orderId;
            document.getElementById('progressValue').value = currentProgress;
            document.getElementById('progressDisplay').textContent = currentProgress + '%';

            new bootstrap.Modal(document.getElementById('progressModal')).show();
        }

        // Обновление отображения прогресса
        document.getElementById('progressValue').addEventListener('input', function() {
            document.getElementById('progressDisplay').textContent = this.value + '%';
        });

        // Обновить прогресс заказа
        async function updateProgress() {
            const orderId = document.getElementById('progressOrderId').value;
            const progress = document.getElementById('progressValue').value;

            try {
                const response = await fetch(`/WorkOrders/UpdateProgress/${orderId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `progress=${progress}&__RequestVerificationToken=${getAntiForgeryToken()}`
                });

                if (response.ok) {
                    bootstrap.Modal.getInstance(document.getElementById('progressModal')).hide();
                    location.reload();
                } else {
                    alert('Ошибка при обновлении прогресса');
                }
            } catch (error) {
                console.error('Ошибка:', error);
                alert('Ошибка при обновлении прогресса');
            }
        }

        // Отменить заказ
        async function cancelOrder(orderId) {
            if (!confirm('Вы уверены, что хотите отменить этот заказ?')) {
                return;
            }

            try {
                const response = await fetch(`/WorkOrders/UpdateStatus/${orderId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `status=Cancelled&__RequestVerificationToken=${getAntiForgeryToken()}`
                });

                if (response.ok) {
                    location.reload();
                } else {
                    alert('Ошибка при отмене заказа');
                }
            } catch (error) {
                console.error('Ошибка:', error);
                alert('Ошибка при отмене заказа');
            }
        }

        // Получение CSRF токена
        function getAntiForgeryToken() {
            const token = document.querySelector('input[name="__RequestVerificationToken"]');
            return token ? token.value : '';
        }
    </script>

    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap5.min.css">
    <script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.10.24/js/dataTables.bootstrap5.min.js"></script>
}
