using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using ProductionManagementSystem.Data;
using ProductionManagementSystem.Models;
using ProductionManagementSystem.Models.ViewModels;

namespace ProductionManagementSystem.Controllers
{
    public class ProductsController : Controller
    {
        private readonly ApplicationDbContext _context;

        public ProductsController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Products
        public async Task<IActionResult> Index(string? search, string? category)
        {
            var query = _context.Products.AsQueryable();

            if (!string.IsNullOrEmpty(search))
            {
                query = query.Where(p => p.Name.Contains(search) || p.Description!.Contains(search));
            }

            if (!string.IsNullOrEmpty(category))
            {
                query = query.Where(p => p.Category == category);
            }

            var products = await query
                .Include(p => p.ProductMaterials)
                .ThenInclude(pm => pm.Material)
                .OrderBy(p => p.Name)
                .ToListAsync();

            // Получаем список категорий для фильтра
            ViewBag.Categories = await _context.Products
                .Select(p => p.Category)
                .Distinct()
                .OrderBy(c => c)
                .ToListAsync();

            ViewBag.Search = search;
            ViewBag.Category = category;

            return View(products);
        }

        // GET: Products/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var product = await _context.Products
                .Include(p => p.ProductMaterials)
                .ThenInclude(pm => pm.Material)
                .FirstOrDefaultAsync(p => p.Id == id);

            if (product == null)
            {
                return NotFound();
            }

            var viewModel = new ProductDetailsViewModel
            {
                Product = product,
                Materials = product.ProductMaterials.Select(pm => new MaterialRequirement
                {
                    MaterialName = pm.Material.Name,
                    RequiredQuantity = pm.QuantityNeeded,
                    AvailableQuantity = pm.Material.Quantity,
                    UnitOfMeasure = pm.Material.UnitOfMeasure
                }).ToList(),
                RecentWorkOrders = await _context.WorkOrders
                    .Where(wo => wo.ProductId == id)
                    .Include(wo => wo.ProductionLine)
                    .OrderByDescending(wo => wo.Id)
                    .Take(10)
                    .ToListAsync(),
                TotalProduced = await _context.WorkOrders
                    .Where(wo => wo.ProductId == id && wo.Status == WorkOrderStatus.Completed)
                    .SumAsync(wo => wo.Quantity),
                PendingOrders = await _context.WorkOrders
                    .CountAsync(wo => wo.ProductId == id && wo.Status == WorkOrderStatus.Pending)
            };

            return View(viewModel);
        }

        // GET: Products/Create
        public async Task<IActionResult> Create()
        {
            ViewBag.Materials = await _context.Materials
                .OrderBy(m => m.Name)
                .Select(m => new SelectListItem
                {
                    Value = m.Id.ToString(),
                    Text = $"{m.Name} ({m.UnitOfMeasure})"
                })
                .ToListAsync();

            return View();
        }

        // POST: Products/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Name,Description,Specifications,Category,MinimalStock,ProductionTimePerUnit")] Product product,
            int[] materialIds, decimal[] quantities)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    _context.Add(product);
                    await _context.SaveChangesAsync();

                    // Добавляем связи с материалами
                    if (materialIds != null && quantities != null && materialIds.Length == quantities.Length)
                    {
                        for (int i = 0; i < materialIds.Length; i++)
                        {
                            if (materialIds[i] > 0 && quantities[i] > 0)
                            {
                                var productMaterial = new ProductMaterial
                                {
                                    ProductId = product.Id,
                                    MaterialId = materialIds[i],
                                    QuantityNeeded = quantities[i]
                                };
                                _context.ProductMaterials.Add(productMaterial);
                            }
                        }
                        await _context.SaveChangesAsync();
                    }

                    TempData["SuccessMessage"] = "Продукт успешно создан.";
                    return RedirectToAction(nameof(Index));
                }
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"Ошибка при создании продукта: {ex.Message}";
            }

            // Если модель невалидна, перезагружаем список материалов
            ViewBag.Materials = await _context.Materials
                .OrderBy(m => m.Name)
                .Select(m => new SelectListItem
                {
                    Value = m.Id.ToString(),
                    Text = $"{m.Name} ({m.UnitOfMeasure})"
                })
                .ToListAsync();

            return View(product);
        }

        // GET: Products/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var product = await _context.Products
                .Include(p => p.ProductMaterials)
                .FirstOrDefaultAsync(p => p.Id == id);

            if (product == null)
            {
                return NotFound();
            }

            ViewBag.Materials = await _context.Materials
                .OrderBy(m => m.Name)
                .Select(m => new SelectListItem
                {
                    Value = m.Id.ToString(),
                    Text = $"{m.Name} ({m.UnitOfMeasure})"
                })
                .ToListAsync();

            return View(product);
        }

        // POST: Products/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,Name,Description,Specifications,Category,MinimalStock,ProductionTimePerUnit")] Product product,
            int[] materialIds, decimal[] quantities)
        {
            if (id != product.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(product);

                    // Удаляем старые связи с материалами
                    var existingMaterials = _context.ProductMaterials.Where(pm => pm.ProductId == id);
                    _context.ProductMaterials.RemoveRange(existingMaterials);

                    // Добавляем новые связи
                    if (materialIds != null && quantities != null && materialIds.Length == quantities.Length)
                    {
                        for (int i = 0; i < materialIds.Length; i++)
                        {
                            if (quantities[i] > 0)
                            {
                                var productMaterial = new ProductMaterial
                                {
                                    ProductId = product.Id,
                                    MaterialId = materialIds[i],
                                    QuantityNeeded = quantities[i]
                                };
                                _context.ProductMaterials.Add(productMaterial);
                            }
                        }
                    }

                    await _context.SaveChangesAsync();
                    TempData["SuccessMessage"] = "Продукт успешно обновлен.";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!ProductExists(product.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }

            ViewBag.Materials = await _context.Materials
                .OrderBy(m => m.Name)
                .Select(m => new SelectListItem
                {
                    Value = m.Id.ToString(),
                    Text = $"{m.Name} ({m.UnitOfMeasure})"
                })
                .ToListAsync();

            return View(product);
        }

        // GET: Products/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var product = await _context.Products
                .Include(p => p.ProductMaterials)
                .ThenInclude(pm => pm.Material)
                .FirstOrDefaultAsync(p => p.Id == id);

            if (product == null)
            {
                return NotFound();
            }

            // Проверяем, есть ли активные заказы
            var hasActiveOrders = await _context.WorkOrders
                .AnyAsync(wo => wo.ProductId == id &&
                         (wo.Status == WorkOrderStatus.Pending || wo.Status == WorkOrderStatus.InProgress));

            ViewBag.HasActiveOrders = hasActiveOrders;

            return View(product);
        }

        // POST: Products/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var product = await _context.Products.FindAsync(id);
            if (product != null)
            {
                // Проверяем, есть ли активные заказы
                var hasActiveOrders = await _context.WorkOrders
                    .AnyAsync(wo => wo.ProductId == id &&
                             (wo.Status == WorkOrderStatus.Pending || wo.Status == WorkOrderStatus.InProgress));

                if (hasActiveOrders)
                {
                    TempData["ErrorMessage"] = "Нельзя удалить продукт с активными заказами.";
                    return RedirectToAction(nameof(Index));
                }

                _context.Products.Remove(product);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "Продукт успешно удален.";
            }

            return RedirectToAction(nameof(Index));
        }

        private bool ProductExists(int id)
        {
            return _context.Products.Any(e => e.Id == id);
        }
    }
}
