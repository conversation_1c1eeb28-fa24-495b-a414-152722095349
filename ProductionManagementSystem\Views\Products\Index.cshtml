@model IEnumerable<ProductionManagementSystem.Models.Product>
@{
    ViewData["Title"] = "Продукты";
}

<div class="d-flex justify-content-between align-items-center mb-4 page-header">
    <h1 class="h2 mb-0">
        <i class="bi bi-box-seam me-2 text-primary"></i>Управление продуктами
    </h1>
    <div class="d-flex gap-2">
        <a asp-action="Create" class="btn btn-primary shadow-sm">
            <i class="bi bi-plus-circle me-2"></i>Добавить продукт
        </a>
    </div>
</div>

<!-- Фильтры и поиск -->
<div class="card mb-4 filter-card">
    <div class="card-header bg-transparent border-0 pb-0">
        <h6 class="mb-0 text-primary">
            <i class="bi bi-funnel me-2"></i>Фильтры и поиск
        </h6>
    </div>
    <div class="card-body pt-3">
        <form method="get" class="row g-3">
            <div class="col-lg-5 col-md-6">
                <label for="search" class="form-label">
                    <i class="bi bi-search me-1"></i>Поиск продуктов
                </label>
                <input type="text" class="form-control shadow-sm" id="search" name="search"
                       value="@ViewBag.Search" placeholder="Введите название или описание продукта">
            </div>
            <div class="col-lg-3 col-md-4">
                <label for="category" class="form-label">
                    <i class="bi bi-tag me-1"></i>Категория
                </label>
                <select class="form-select shadow-sm" id="category" name="category">
                    <option value="">Все категории</option>
                    @if (ViewBag.Categories != null)
                    {
                        @foreach (var cat in ViewBag.Categories)
                        {
                            <option value="@cat" selected="@(ViewBag.Category == cat)">@cat</option>
                        }
                    }
                </select>
            </div>
            <div class="col-lg-4 col-md-2 d-flex align-items-end gap-2">
                <button type="submit" class="btn btn-outline-primary shadow-sm flex-fill">
                    <i class="bi bi-search me-1"></i>Применить
                </button>
                <a href="@Url.Action("Index")" class="btn btn-outline-secondary shadow-sm flex-fill">
                    <i class="bi bi-arrow-clockwise me-1"></i>Сброс
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Таблица продуктов -->
<div class="card shadow">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">
            Список продуктов (@Model.Count())
        </h6>
    </div>
    <div class="card-body">
        @if (Model.Any())
        {
            <div class="table-responsive">
                <table class="table table-hover" id="productsTable">
                    <thead class="table-light">
                        <tr>
                            <th>Название</th>
                            <th>Категория</th>
                            <th>Описание</th>
                            <th>Время производства</th>
                            <th>Минимальный запас</th>
                            <th>Материалы</th>
                            <th>Действия</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var product in Model)
                        {
                            <tr>
                                <td>
                                    <div class="fw-bold">@product.Name</div>
                                    @if (!string.IsNullOrEmpty(product.Specifications))
                                    {
                                        <small class="text-muted">@product.Specifications</small>
                                    }
                                </td>
                                <td>
                                    <span class="badge bg-info">@product.Category</span>
                                </td>
                                <td>
                                    @if (!string.IsNullOrEmpty(product.Description))
                                    {
                                        <span>@(product.Description.Length > 50 ? product.Description.Substring(0, 50) + "..." : product.Description)</span>
                                    }
                                </td>
                                <td>
                                    <span class="fw-bold">@product.ProductionTimePerUnit</span> мин/ед
                                </td>
                                <td>
                                    <span class="@(product.MinimalStock > 0 ? "text-warning" : "text-muted")">
                                        @product.MinimalStock
                                    </span>
                                </td>
                                <td>
                                    @if (product.ProductMaterials?.Any() == true)
                                    {
                                        <span class="badge bg-secondary">@product.ProductMaterials.Count материалов</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted">Не указаны</span>
                                    }
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a asp-action="Details" asp-route-id="@product.Id" 
                                           class="btn btn-sm btn-outline-info" title="Подробности">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a asp-action="Edit" asp-route-id="@product.Id" 
                                           class="btn btn-sm btn-outline-primary" title="Редактировать">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <a asp-action="Delete" asp-route-id="@product.Id" 
                                           class="btn btn-sm btn-outline-danger" title="Удалить">
                                            <i class="bi bi-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="text-center py-5">
                <i class="bi bi-box-seam display-1 text-muted"></i>
                <h4 class="text-muted mt-3">Продукты не найдены</h4>
                <p class="text-muted">
                    @if (!string.IsNullOrEmpty(ViewBag.Search as string) || !string.IsNullOrEmpty(ViewBag.Category as string))
                    {
                        <span>Попробуйте изменить параметры поиска или </span>
                        <a href="@Url.Action("Index")" class="text-decoration-none">сбросить фильтры</a>
                    }
                    else
                    {
                        <span>Начните с </span>
                        <a asp-action="Create" class="text-decoration-none">добавления первого продукта</a>
                    }
                </p>
            </div>
        }
    </div>
</div>

@section Scripts {
    <script>
        // Инициализация DataTable
        $(document).ready(function() {
            if ($('#productsTable tbody tr').length > 0) {
                $('#productsTable').DataTable({
                    "language": {
                        "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Russian.json"
                    },
                    "pageLength": 25,
                    "order": [[0, "asc"]],
                    "columnDefs": [
                        { "orderable": false, "targets": [6] }
                    ]
                });
            }
        });
    </script>
    
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap5.min.css">
    <script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.10.24/js/dataTables.bootstrap5.min.js"></script>
}
