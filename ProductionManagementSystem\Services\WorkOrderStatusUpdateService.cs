using Microsoft.EntityFrameworkCore;
using ProductionManagementSystem.Data;
using ProductionManagementSystem.Models;

namespace ProductionManagementSystem.Services
{
    public class WorkOrderStatusUpdateService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<WorkOrderStatusUpdateService> _logger;

        public WorkOrderStatusUpdateService(
            IServiceProvider serviceProvider,
            ILogger<WorkOrderStatusUpdateService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await UpdateWorkOrderStatuses();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Ошибка при обновлении статусов заказов");
                }

                // Проверяем каждые 30 секунд
                await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);
            }
        }

        private async Task UpdateWorkOrderStatuses()
        {
            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

            var now = DateTime.Now;

            // Находим заказы, которые должны начаться
            var ordersToStart = await context.WorkOrders
                .Where(wo => wo.Status == WorkOrderStatus.Pending && wo.StartDate <= now)
                .ToListAsync();

            foreach (var order in ordersToStart)
            {
                order.Status = WorkOrderStatus.InProgress;
                order.ActualStartDate = now;
                _logger.LogInformation($"Заказ {order.Id} начат автоматически");
            }

            // Находим заказы, которые должны завершиться
            var ordersToComplete = await context.WorkOrders
                .Where(wo => wo.Status == WorkOrderStatus.InProgress && wo.EstimatedEndDate <= now)
                .ToListAsync();

            foreach (var order in ordersToComplete)
            {
                order.Status = WorkOrderStatus.Completed;
                order.ActualEndDate = now;
                _logger.LogInformation($"Заказ {order.Id} завершен автоматически");
            }

            if (ordersToStart.Any() || ordersToComplete.Any())
            {
                await context.SaveChangesAsync();
                _logger.LogInformation($"Обновлено статусов: {ordersToStart.Count + ordersToComplete.Count}");
            }
        }
    }
}
