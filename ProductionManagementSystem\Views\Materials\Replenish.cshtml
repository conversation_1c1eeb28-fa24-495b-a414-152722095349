@{
    ViewData["Title"] = "Пополнить запас";
    var material = ViewBag.Material as ProductionManagementSystem.Models.Material;
}

@if (material == null)
{
    <div class="alert alert-danger">
        <h4>Ошибка</h4>
        <p>Материал не найден.</p>
        <a asp-action="Index" class="btn btn-primary">Вернуться к списку</a>
    </div>
}
else
{

<div class="row">
    <div class="col-md-6 mx-auto">
        <div class="card shadow">
            <div class="card-header py-3 @(material.IsLowStock ? "bg-warning" : "bg-primary") text-white">
                <h6 class="m-0 font-weight-bold">
                    <i class="bi bi-plus-circle me-2"></i>Пополнение запаса материала
                </h6>
            </div>
            <div class="card-body">
                <!-- Информация о материале -->
                <div class="alert @(material.IsLowStock ? "alert-warning" : "alert-info")" role="alert">
                    <h5 class="alert-heading">@material.Name</h5>
                    <hr>
                    <div class="row">
                        <div class="col-6">
                            <strong>Текущий запас:</strong><br>
                            <span class="h5">@material.Quantity.ToString("N2") @material.UnitOfMeasure</span>
                        </div>
                        <div class="col-6">
                            <strong>Минимальный запас:</strong><br>
                            <span class="h5">@material.MinimalStock.ToString("N2") @material.UnitOfMeasure</span>
                        </div>
                    </div>
                    @if (material.IsLowStock)
                    {
                        <div class="mt-2">
                            <i class="bi bi-exclamation-triangle me-1"></i>
                            <strong>Внимание!</strong> Запас материала ниже минимального уровня.
                        </div>
                    }
                </div>

                <!-- Форма пополнения -->
                <form asp-action="Replenish" asp-route-id="@material.Id" method="post">
                    @Html.AntiForgeryToken()

                    @if (TempData["ErrorMessage"] != null)
                    {
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle me-2"></i>@TempData["ErrorMessage"]
                        </div>
                    }

                    <div class="mb-3">
                        <label for="quantity" class="form-label">
                            <i class="bi bi-plus-circle me-1"></i>Количество для пополнения
                        </label>
                        <div class="input-group">
                            <input type="number" class="form-control shadow-sm" id="quantity" name="quantity"
                                   step="0.01" min="0.01" placeholder="0.00" required />
                            <span class="input-group-text">@material.UnitOfMeasure</span>
                        </div>
                        <div class="form-text">
                            Введите количество материала, которое нужно добавить к текущему запасу
                        </div>
                    </div>

                    <!-- Предварительный расчет -->
                    <div class="card bg-light mb-3" id="calculation" style="display: none;">
                        <div class="card-body">
                            <h6 class="card-title">Предварительный расчет:</h6>
                            <div class="row">
                                <div class="col-6">
                                    <small class="text-muted">Текущий запас:</small><br>
                                    <span id="current-stock">@material.Quantity.ToString("N2")</span> @material.UnitOfMeasure
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">После пополнения:</small><br>
                                    <span id="new-stock" class="fw-bold">-</span> @material.UnitOfMeasure
                                </div>
                            </div>
                            <div class="mt-2">
                                <span id="status-indicator"></span>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between gap-2">
                        <a asp-action="Index" class="btn btn-outline-secondary shadow-sm">
                            <i class="bi bi-arrow-left me-2"></i>Назад к списку
                        </a>
                        <button type="submit" class="btn btn-success shadow-sm" id="submit-btn">
                            <i class="bi bi-check-circle me-2"></i>Пополнить запас
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        const currentStock = @material.Quantity;
        const minimalStock = @material.MinimalStock;
        const quantityInput = document.getElementById('quantity');
        const calculationCard = document.getElementById('calculation');
        const newStockSpan = document.getElementById('new-stock');
        const statusIndicator = document.getElementById('status-indicator');
        const submitBtn = document.getElementById('submit-btn');

        quantityInput.addEventListener('input', function() {
            const addQuantity = parseFloat(this.value) || 0;

            if (addQuantity > 0) {
                calculationCard.style.display = 'block';
                const newStock = currentStock + addQuantity;
                newStockSpan.textContent = newStock.toFixed(2);

                if (newStock >= minimalStock) {
                    statusIndicator.innerHTML = '<i class="bi bi-check-circle text-success me-1"></i><span class="text-success">Запас будет достаточным</span>';
                } else {
                    statusIndicator.innerHTML = '<i class="bi bi-exclamation-triangle text-warning me-1"></i><span class="text-warning">Запас все еще будет ниже минимального</span>';
                }
            } else {
                calculationCard.style.display = 'none';
            }
        });

        // Быстрые кнопки для пополнения
        function addQuickAmount(amount) {
            quantityInput.value = amount;
            quantityInput.dispatchEvent(new Event('input'));
        }

        // Добавляем быстрые кнопки
        const quickButtonsHtml = `
            <div class="mb-3">
                <label class="form-label">Быстрое пополнение:</label><br>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="addQuickAmount(10)">+10</button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="addQuickAmount(50)">+50</button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="addQuickAmount(100)">+100</button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="addQuickAmount(${Math.max(0, minimalStock - currentStock).toFixed(0)})">До минимума</button>
                </div>
            </div>
        `;

        quantityInput.parentNode.parentNode.insertAdjacentHTML('afterend', quickButtonsHtml);
    </script>
}

}
