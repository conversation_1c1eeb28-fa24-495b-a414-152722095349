using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using ProductionManagementSystem.Data;
using ProductionManagementSystem.Models;

namespace ProductionManagementSystem.Controllers
{
    public class MaterialsController : Controller
    {
        private readonly ApplicationDbContext _context;

        public MaterialsController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Materials
        public async Task<IActionResult> Index(string? search, bool? lowStock)
        {
            var query = _context.Materials.AsQueryable();

            if (!string.IsNullOrEmpty(search))
            {
                query = query.Where(m => m.Name.Contains(search) || m.UnitOfMeasure.Contains(search));
            }

            if (lowStock == true)
            {
                query = query.Where(m => m.Quantity <= m.MinimalStock);
            }

            var materials = await query.OrderBy(m => m.Name).ToListAsync();

            ViewBag.Search = search;
            ViewBag.LowStock = lowStock;

            return View(materials);
        }

        // GET: Materials/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var material = await _context.Materials
                .Include(m => m.ProductMaterials)
                .ThenInclude(pm => pm.Product)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (material == null)
            {
                return NotFound();
            }

            return View(material);
        }

        // GET: Materials/Create
        public IActionResult Create()
        {
            return View();
        }

        // POST: Materials/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Name,Quantity,UnitOfMeasure,MinimalStock")] Material material)
        {
            if (ModelState.IsValid)
            {
                _context.Add(material);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "Материал успешно создан.";
                return RedirectToAction(nameof(Index));
            }
            return View(material);
        }

        // GET: Materials/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var material = await _context.Materials.FindAsync(id);
            if (material == null)
            {
                return NotFound();
            }
            return View(material);
        }

        // POST: Materials/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,Name,Quantity,UnitOfMeasure,MinimalStock")] Material material)
        {
            if (id != material.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(material);
                    await _context.SaveChangesAsync();
                    TempData["SuccessMessage"] = "Материал успешно обновлен.";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!MaterialExists(material.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            return View(material);
        }

        // GET: Materials/Replenish/5
        public async Task<IActionResult> Replenish(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var material = await _context.Materials.FindAsync(id);
            if (material == null)
            {
                return NotFound();
            }

            ViewBag.Material = material;
            return View();
        }

        // POST: Materials/Replenish/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Replenish(int id, decimal quantity)
        {
            try
            {
                var material = await _context.Materials.FindAsync(id);
                if (material == null)
                {
                    TempData["ErrorMessage"] = "Материал не найден.";
                    return RedirectToAction(nameof(Index));
                }

                if (quantity <= 0)
                {
                    TempData["ErrorMessage"] = "Количество должно быть больше нуля.";
                    ViewBag.Material = material;
                    return View();
                }

                material.Quantity += quantity;
                await _context.SaveChangesAsync();

                TempData["SuccessMessage"] = $"Запас материала '{material.Name}' пополнен на {quantity:N2} {material.UnitOfMeasure}.";
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"Ошибка при пополнении запаса: {ex.Message}";
                return RedirectToAction(nameof(Index));
            }
        }

        // GET: Materials/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var material = await _context.Materials
                .FirstOrDefaultAsync(m => m.Id == id);
            if (material == null)
            {
                return NotFound();
            }

            // Проверяем, используется ли материал
            var isUsed = await _context.ProductMaterials.AnyAsync(pm => pm.MaterialId == id);
            ViewBag.IsUsed = isUsed;

            return View(material);
        }

        // POST: Materials/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var material = await _context.Materials.FindAsync(id);
            if (material != null)
            {
                // Проверяем, используется ли материал
                var isUsed = await _context.ProductMaterials.AnyAsync(pm => pm.MaterialId == id);
                if (isUsed)
                {
                    TempData["ErrorMessage"] = "Нельзя удалить материал, который используется в продуктах.";
                    return RedirectToAction(nameof(Index));
                }

                _context.Materials.Remove(material);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "Материал успешно удален.";
            }

            return RedirectToAction(nameof(Index));
        }

        private bool MaterialExists(int id)
        {
            return _context.Materials.Any(e => e.Id == id);
        }
    }
}
