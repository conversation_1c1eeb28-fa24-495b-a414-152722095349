using Microsoft.EntityFrameworkCore;
using ProductionManagementSystem.Data;
using ProductionManagementSystem.Models;
using ProductionManagementSystem.Models.ViewModels;

namespace ProductionManagementSystem.Services
{
    public class ProductionCalculationService : IProductionCalculationService
    {
        private readonly ApplicationDbContext _context;

        public ProductionCalculationService(ApplicationDbContext context)
        {
            _context = context;
        }

        public int CalculateProductionTime(int productId, int quantity, float efficiencyFactor = 1.0f)
        {
            var product = _context.Products.Find(productId);
            if (product == null) return 0;

            return (int)Math.Ceiling((quantity * product.ProductionTimePerUnit) / efficiencyFactor);
        }

        public DateTime CalculateEstimatedEndDate(DateTime startDate, int productionTimeMinutes)
        {
            // Просто добавляем минуты к дате начала
            return startDate.AddMinutes(productionTimeMinutes);
        }

        public async Task<bool> CheckMaterialAvailabilityAsync(int productId, int quantity)
        {
            var requirements = await GetMaterialRequirementsAsync(productId, quantity);
            return requirements.All(r => r.IsSufficient);
        }

        public async Task<List<MaterialRequirement>> GetMaterialRequirementsAsync(int productId, int quantity)
        {
            var productMaterials = await _context.ProductMaterials
                .Include(pm => pm.Material)
                .Where(pm => pm.ProductId == productId)
                .ToListAsync();

            var requirements = new List<MaterialRequirement>();

            foreach (var pm in productMaterials)
            {
                var requiredQuantity = pm.QuantityNeeded * quantity;
                requirements.Add(new MaterialRequirement
                {
                    MaterialName = pm.Material.Name,
                    RequiredQuantity = requiredQuantity,
                    AvailableQuantity = pm.Material.Quantity,
                    UnitOfMeasure = pm.Material.UnitOfMeasure
                });
            }

            return requirements;
        }

        public async Task<ProductionLine?> FindAvailableProductionLineAsync()
        {
            return await _context.ProductionLines
                .Where(pl => pl.Status == ProductionLineStatus.Active && pl.CurrentWorkOrderId == null)
                .OrderByDescending(pl => pl.EfficiencyFactor)
                .FirstOrDefaultAsync();
        }

        public async Task<bool> ReserveMaterialsAsync(int productId, int quantity)
        {
            var productMaterials = await _context.ProductMaterials
                .Include(pm => pm.Material)
                .Where(pm => pm.ProductId == productId)
                .ToListAsync();

            foreach (var pm in productMaterials)
            {
                var requiredQuantity = pm.QuantityNeeded * quantity;
                if (pm.Material.Quantity < requiredQuantity)
                {
                    return false; // Недостаточно материала
                }
                pm.Material.Quantity -= requiredQuantity;
            }

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task ReleaseMaterialsAsync(int productId, int quantity)
        {
            var productMaterials = await _context.ProductMaterials
                .Include(pm => pm.Material)
                .Where(pm => pm.ProductId == productId)
                .ToListAsync();

            foreach (var pm in productMaterials)
            {
                var requiredQuantity = pm.QuantityNeeded * quantity;
                pm.Material.Quantity += requiredQuantity;
            }

            await _context.SaveChangesAsync();
        }
    }
}
